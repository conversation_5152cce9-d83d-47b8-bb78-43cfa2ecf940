/*
   Copyright (c) 2022 Ham <PERSON>
   MTK_GPU_GOV is licensed under Mulan PSL v2.
   You can use this software according to the terms and conditions of the Mulan PSL v2.
   You may obtain a copy of Mulan PSL v2 at:
               http://license.coscl.org.cn/MulanPSL2
   THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
   See the Mulan PSL v2 for more details.
 */
/*
	MTK_GPU_GOV
	File:			FilePath.hpp
	Author: 		Ham<PERSON><PERSON> @CoolApk
	Create Date: 	2022/03/04
	Last Update: 	2022/04/06
*/
#pragma once

#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wunknown-pragmas"
#pragma ide diagnostic ignored "EndlessLoop"

#include <string_view>

constexpr std::string_view MainThread = "LoadMonitor";
constexpr std::string_view GameThread = "GameModeWatcher";
constexpr std::string_view ConfThread = "ConfigWatcher";
constexpr std::string_view GEDLocker = "GedLocker";
constexpr std::string_view GameModePath = "/dev/asopt_game";
constexpr std::string_view module_load = "/sys/module/ged/parameters/gpu_loading";
constexpr std::string_view module_idle = "/sys/module/ged/parameters/gpu_idle";
constexpr std::string_view kernel_load = "/sys/kernel/ged/hal/gpu_utilization";
constexpr std::string_view kernel_debug_load = "/sys/kernel/d/ged/hal/gpu_utilization";
constexpr std::string_view gpu_current_freq_path = "/sys/kernel/ged/hal/current_freqency";
constexpr std::string_view kernel_d_load = "/sys/kernel/debug/ged/hal/gpu_utilization";
constexpr std::string_view gpu_freq_LoadPath = "/proc/gpufreq/gpufreq_var_dump";
constexpr std::string_view proc_mali_load = "/proc/mali/utilization";
constexpr std::string_view proc_mtk_load = "/proc/mtk_mali/utilization";
constexpr std::string_view debug_dvfs_load = "/sys/kernel/debug/mali0/dvfs_utilization";
constexpr std::string_view debug_dvfs_load_old = "/proc/mali/dvfs_utilization";
constexpr std::string_view gpufreq_table = "/proc/gpufreq/gpufreq_opp_dump";
constexpr std::string_view gpufreqv2_table = "/proc/gpufreqv2/stack_working_opp_table";
constexpr std::string_view gedfreq_max = "/sys/kernel/ged/hal/custom_upbound_gpu_freq";
constexpr std::string_view gedfreq_min = "/sys/kernel/ged/hal/custom_boost_gpu_freq";
constexpr std::string_view gpufreq_opp = "/proc/gpufreq/gpufreq_opp_freq";
constexpr std::string_view gpufreqv2_opp = "/proc/gpufreqv2/fix_target_opp_index";
constexpr std::string_view gpufreq_volt = "/proc/gpufreq/gpufreq_fixed_freq_volt";
constexpr std::string_view gpufreqv2_volt = "/proc/gpufreqv2/fix_custom_freq_volt";
constexpr std::string_view config_file_def = "/sdcard/Android/hamjin/gpu_gov/gpufreq.conf";
constexpr std::string_view config_file_tr = "/data/gpu_freq_table.conf";
constexpr std::string_view log_path = "/sdcard/Android/hamjin/gpu_gov/gpu_gov.log.txt";
constexpr std::string_view gpu_power_policy = "/sys/class/misc/mali0/device/power_policy";

[[maybe_unused]] constexpr std::string_view fw = "FreqWriter";
constexpr std::string_view fps_status = "/sys/kernel/fpsgo/fstb/fpsgo_status";
[[maybe_unused]] constexpr std::string_view top_pid = "/sys/kernel/gbe/gbe2_fg_pid";

#define respTime 16