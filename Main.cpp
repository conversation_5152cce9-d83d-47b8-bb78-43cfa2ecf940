/*
   Copyright (c) 2022 Ham Jin
   loadmonitor_gpuv2 is licensed under Mulan PSL v2.
   You can use this software according to the terms and conditions of the Mulan PSL v2.
   You may obtain a copy of Mulan PSL v2 at:
               http://license.coscl.org.cn/MulanPSL2
   THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
   See the Mulan PSL v2 for more details.
 */
/**
  *	Mediatek Mali GPU Load-Based Frequency Adjustment
  * File:			Main.cpp
  * Author: 		HamJin @CoolApk
  * Create Date: 	2022/03/04
  * Last Update: 	2022/12/13
  * Feature:		Help and bootstrap Main Work Function
*/

#include "FilePath.hpp"
#include "FileStatus.hpp"
#include "FreqTable.hpp"
#include "LoadMonitor.hpp"
#include "Logger.hpp"
#include "NodeMonitor.hpp"
#include "FileOperate.hpp"
#include <cstring>
#include <thread>


using namespace std;
using namespace logger;

constexpr std::string_view Notes = "Mediatek Mali GPU Governor";
constexpr std::string_view Author = "Author: walika @CoolApk";
constexpr std::string_view Special = "Special Thanks: HamJin @CoolApk,asto18089 @CoolApk and helloklf @Github";
constexpr std::string_view Version = "Version: v2.0";
//constexpr std::string_view gpu_on = "always_on";
//constexpr std::string_view gpu_idle = "coarse_ondemand";

/**
 * main: main function
 * @param argc argument count
 * @param argv arguments value
 * @return return value
 */
[[maybe_unused]] [[noreturn]] static __always_inline void lockGed();

[[noreturn]] static __always_inline void main_func();

int main(int argc, char *argv[]) {
    if (argc > 1)
    {
        for (int i = 1; i < argc; ++i)
        {
            string_view cur_arg = argv[i];
            if (cur_arg == "-h")
            {
                puts(Notes.data());
                puts(Author.data());
                puts(Special.data());
                puts("Usage:\n\t-v show version\n\t-h show help");
                return 0;
            }
            else if (cur_arg == "-v")
            {
                puts(Notes.data());
                puts(Author.data());
                puts(Special.data());
                puts(Version.data());
                return 0;
            }
        }
    }
    main_func();
}

static __always_inline void main_func() {
    LogOut_info << Notes;
    LogOut_info << Author;
    LogOut_info << Special;
    LogOut_info << Version;

    // Init
    GPUs GPU = {};
    LogOut_info << "Loading";
    gpufreqTableInit(GPU);
    utilization_init();
    GPU.setPrecise(getStatus(debug_dvfs_load) || getStatus(debug_dvfs_load_old));
    thread asopt_monitor([&GPU]()
                         { monitor_gaming(GPU); });
    thread config_monitor([&GPU]()
                          { monitor_config(GPU); });

    asopt_monitor.detach(), config_monitor.detach();
    LogOut_info << "Monitor Inited";
    this_thread::sleep_for(5s);

    GPU.setCurFreq(GPU.getFreqByIndex(0));
    GPU.genCurVolt();
    if (getStatus(debug_dvfs_load) || getStatus(debug_dvfs_load_old))
        GPU.setPrecise(true);
    // bootstrap information
    LogOut_info << "BootFreq: " << GPU.getCurFreq() << "KHz";
    LogOut_info << "Driver: gpufreq" << (GPU.isGpuv2() ? "v2" : "v1");
    LogOut_info << "Is Precise: " << (GPU.isPrecise() ? "Yes" : "No");
    LogOut_info << "Governor Started";

    //Writers
//    thread gedLockerThread([]()
//                           { lockGed(); });
//    gedLockerThread.detach();
    GPU.adjustGpufreq();
}

/**
 * GPU Freq Adj
 * @param GPU the GPU we operate
 * @param utilization current utilization
 */
[[maybe_unused]] [[noreturn]] static __always_inline void lockGed() {
    pthread_setname_np(pthread_self(), GEDLocker.data());
    const char idm[] = "99", idM[] = "0";
    LogOut_info << "Locking GED Freq";
    while (true)
    {
        WriteFile(gedfreq_min, idm, 2);
        WriteFile(gedfreq_max, idM, 1);
        this_thread::sleep_for(1s);
    }
}

static __always_inline int getCurFreqFromSystem() {
    int curFreq;
    string buf;
    ReadFile(gpu_current_freq_path, buf, 64);
    sscanf(buf.c_str(), "%*d %d", &curFreq);//NOLINT
    return curFreq;
}

static __always_inline int getCurFreqFromSystemV1() {
    int curFreq;
    con_dbg << "open";
    FILE *fp = fopen(gpu_freq_LoadPath.data(), "r");
    if (fp == nullptr)
    {
        LogOut_falt << "Could not get cur freq!";
        LogOut_falt << strerror(errno);
        return 0;
    }
    con_dbg << "getline";
    size_t len = 2048;
    char *buf = (char *) malloc(sizeof(char) * len);
    while (getline(&buf, &len, fp) != EOF)
    {
        con_dbg << "sscanf";
        if (strlen(buf) <= 3) continue;
        sscanf(buf, "idx: %*d, freq: %d, vgpu: %*d, vsram_gpu: %*d", &curFreq);//NOLINT
        if (curFreq != 0) break;
        sscanf(buf, "Freq: %d, Vgpu: %*d, Vsram_gpu: %*d", &curFreq);//NOLINT
        if (curFreq != 0) break;
    }
    fclose(fp);
    free(buf);
    return curFreq;
}

void GPUs::adjustGpufreq() {
    // rename thread
    pthread_setname_np(pthread_self(), MainThread.data());
    const int margin = precise ? 15 : 20;
    int util;
    long now_freq;
    long targetFreq;
    long finalFreq;
    long finalFreqIndex;
//    bool no_fpsgo = !checkRead(fps_status);
    while (true)
    {
        util = DebugUtilization();
//        if (util <= 50 && !isPrecise())
//        {
//            for (int i = 1; i <= 5 && util <= 50; i++)
//            {
//                util = DebugUtilization();
//                this_thread::sleep_for(8ms);
//            }
//        }
//        now_freq = getCurFreq();
//        if (now_freq == 0 || no_fpsgo)
//        {
        if (gpuv2) now_freq = getCurFreqFromSystem();
        else now_freq = getCurFreqFromSystemV1();
//        }

        con_dbg << "now_freq " << now_freq << " util=" << util;

        targetFreq = now_freq * (util + margin) / 100;
        if (now_freq < targetFreq)
            finalFreq = readFreqGe(targetFreq);
        else
            finalFreq = readFreqLe(targetFreq);
#ifdef DCS_ENABLE
        if (finalFreq < getFreqByIndex(0) && isGpuv2())
            setNeedDcs(true);
        else
            setNeedDcs(false);
#endif
        finalFreqIndex = readFreqIndex(finalFreq);
        con_dbg << "target_freq:" << targetFreq
                << ",cur_freq:" << curFreq
                << ",final_freq:" << finalFreq
                << ",down_freq:" << genCurFreq(curFreqIdx - 1)
                << ",up_freq:" << genCurFreq(finalFreqIndex);
        if (finalFreq > curFreq ||
            (finalFreq == curFreq && targetFreq > curFreq))
        {
            con_dbg << "go up";
            curFreq = genCurFreq(finalFreqIndex);
            curFreqIdx = finalFreqIndex;
            load_low = 0;
            goto GENVOLT;
        }
        if (targetFreq > (genCurFreq(curFreqIdx - 1) + curFreq) >> 1)
        {
            con_dbg << "keep cur";
            load_low = 0;
            goto GENVOLT;
        }
        if (load_low > INT_MAX)
            load_low = 500;
        ++load_low;
        if (precise)
        {
            if (gaming_mode && load_low > 0)
            {
                con_dbg << "go down";
                curFreq = genCurFreq(curFreqIdx - 1);
                --curFreqIdx;
                goto GENVOLT;
            }
            if (load_low > 3)
            {
                con_dbg << "go down";
                curFreq = genCurFreq(curFreqIdx - 1);
                --curFreqIdx;
                goto GENVOLT;
            }
        }
        if (gaming_mode && load_low > 3)
        {
            con_dbg << "go down";
            curFreq = genCurFreq(curFreqIdx - 1);
            --curFreqIdx;
            goto GENVOLT;
        }
        if (load_low > 5)
        {
            con_dbg << "go down";
            curFreq = genCurFreq(curFreqIdx - 1);
            --curFreqIdx;
            goto GENVOLT;
        }
        con_dbg << "detect down";
        GENVOLT:
        if (load_low >= 60)
            is_idle = true;
        if (util > 50)
            is_idle = false;
        curVolt = genCurVolt();
        writeFreq();
        if (is_idle)
        {
//            WriteFile(gpu_power_policy, gpu_idle, gpu_idle.length());
            if (precise)
                this_thread::sleep_for(200ms);
            else
                this_thread::sleep_for(160ms);
            continue;
        }
        is_idle = false;
//            WriteFile(gpu_power_policy, gpu_on, gpu_on.length());
        if (precise) continue;
        else this_thread::sleep_for(respTime * 1ms);
    }

}
