/*
   Copyright (c) 2022 Ham Jin
   loadmonitor_gpuv2 is licensed under Mulan PSL v2.
   You can use this software according to the terms and conditions of the Mulan PSL v2.
   You may obtain a copy of Mulan PSL v2 at:
               http://license.coscl.org.cn/MulanPSL2
   THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
   See the Mulan PSL v2 for more details.
 */
/**
 * Mediatek Mali GPU Load-Based Frequency Adjustment
 * File:			GPU.hpp
 * Author: 		    HamJin @CoolApk
 * Create Date: 	2022/08/13
 * Last Update: 	2022/12/13
 * Feature:        GPU Definition
 */

#pragma once

#include <unordered_map>
#include <vector>

using std::vector, std::unordered_map;

enum TabType {
    freqvolt,
    freqdram,
    defvolt
};
enum ListType {
    CfgList,
    DefFreqList
};

class GPUs {
public:

    long getFreqByIndex(long Idx);

    long getVolt(long freq);

    [[noreturn]]void adjustGpufreq();

    __always_inline void writeFreq() const;

    long getCurVolt() const;

    void setCurVolt(long);

    [[maybe_unused]] void insertToList(ListType T, long freq);

    [[maybe_unused]] void replaceList(ListType T, vector<long> &list);

    vector<long> getList(ListType T) const;

    vector<long> getGpufreqList() const;

    vector<long> getConfigList() const;

    void setConfigList(vector<long> &config_List);

    void setGpufreqList(vector<long> &gpufreq_List);

    void insertToTab(TabType T, long freq, long data);

    void replaceTab(TabType T, unordered_map<long, long> &Tb);

    long readTab(TabType T, long freq);

    long getCurFreq() const;

    void setCurFreq(long);

    long getCurFreqIdx() const;

    void setCurFreqIdx(long);

    bool isGpuv2() const;

    void setGpuv2(bool);

    [[maybe_unused]] bool isDcsEnable() const;

    void setDcsEnable(bool);

    bool isGamingMode() const;

    void setGamingMode(bool);

    long genCurVolt();

    long getLoadLow() const;

    void setLoadLow(long loadLow);

    bool isIdle() const;

    void setIsIdle(bool isIdle);

    bool isNeedDcs() const;

    void setNeedDcs(bool needDcs);

    [[maybe_unused]] bool isHaveconfig() const;

    [[maybe_unused]] void setHaveconfig(bool HaveCFG);

    bool isPrecise() const;

    void setPrecise(bool precise);

private:
    vector<long> gpufreqList{};
    vector<long> configList{};
    unordered_map<long, long> freqVolt{}, freqDram{}, defVolt{};
    long curFreq{};
    long curFreqIdx{};
    long curVolt{};
    long load_low{};
    bool need_dcs{};
    bool is_idle{};
    bool gpuv2{};
    bool dcsEnable{};
    bool gaming_mode{};
    bool haveconfig{};
    bool precise{};

    inline long unify_id(long);

    inline long conf2tab(long) const;

    [[maybe_unused]] inline long tab2conf(long);

    long genCurFreq(long);

    long readFreqGe(long);

    long readFreqLe(long);

    long readFreqIndex(long);

};
