/*
   Copyright (c) 2022 Ham Jin
   loadmonitor_gpuv2 is licensed under Mulan PSL v2.
   You can use this software according to the terms and conditions of the Mulan PSL v2.
   You may obtain a copy of Mulan PSL v2 at:
               http://license.coscl.org.cn/MulanPSL2
   THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
   See the Mulan PSL v2 for more details.
 */
/**
 * Mediatek Mali GPU Load-Based Frequency Adjustment
 * File:			ConfigParser.cpp
 * Author: 		    HamJin @CoolApk
 * Create Date:     2022/09/12
 * Last Update: 	2022/12/13
**/
#include "ConfigParser.hpp"
#include "Logger.hpp"
#include "inotify.hpp"
#include "strstrip.hpp"
#include "FileOperate.hpp"
#include <cstring>

using namespace std;
using namespace logger;


constexpr static __always_inline bool isInTable(const vector<long> &vec, long freq) {
    return std::find(vec.cbegin(), vec.cend(), freq) != vec.cend();
}

constexpr static __always_inline bool VoltIsValid(long v) {
    return v != 0 && v % 625 == 0;
}

void GenDefaultFreqTable(GPUs &gpu) {
    std::vector<long> new_configList;
    for (const auto &freq: gpu.getGpufreqList())
    {
        new_configList.push_back(freq);
        gpu.insertToTab(freqvolt, freq, gpu.readTab(defvolt, freq));
    }
    gpu.setConfigList(new_configList);
}

void config_read(const string_view &config_file, GPUs &gpu) {
    ifstream config_in(config_file.data(), ios::in);
    vector<long> new_configList;
    unordered_map<long, long> new_fvtab, new_fdtab;
    string tmp;
    while (getline(config_in, tmp))
    {
        trim(tmp);
        if (tmp.empty() || tmp.starts_with('#')) continue;
        LogOut_dbg << tmp;
        long freq, volt, dram;
        sscanf(tmp.c_str(), "%lu %lu %lu", &freq, &volt, &dram);//NOLINT
        if (!isInTable(gpu.getGpufreqList(), freq) || !VoltIsValid(volt))
        {
            LogOut_err << tmp << " is invalid: "
                       << "isInTable=" << isInTable(gpu.getGpufreqList(), freq)
                       << ",isVoltValid=" << VoltIsValid(volt);
            continue;

        }
        new_configList.push_back(freq);
        new_fvtab[freq] = volt;
        new_fdtab[freq] = dram;
        if (new_configList.size() >= 9) break;
    }
    config_in.close();
    config_in.clear();
    if (new_configList.empty())
    {
        LogOut_falt << "Reload config FAILED, generating default config";
        GenDefaultFreqTable(gpu);
        return;
    }
    sort(new_configList.begin(), new_configList.end(), less());
    for (const auto &freq: gpu.getGpufreqList())
    {
        if (gpu.readTab(defvolt, freq) < gpu.getVolt(*new_configList.begin()))
        {
            new_configList.push_back(freq);
            new_fvtab[freq] = gpu.readTab(defvolt, freq);
            new_fdtab[freq] = 999;
        }
    }
    sort(new_configList.begin(), new_configList.end(), less());
    gpu.setConfigList(new_configList);
    gpu.replaceTab(freqvolt, new_fvtab);
    gpu.replaceTab(freqdram, new_fdtab);
    LogOut_info << "Load config succeed";
    for (const auto &freq: gpu.getConfigList())
    {
        LogOut_info << "Freq=" << freq
                    << ",Volt=" << gpu.readTab(freqvolt, freq)
                    << ",Dram=" << gpu.readTab(freqdram, freq);
    }
}
