cmake_minimum_required(VERSION 3.18)
project(loadmonitor_gpu)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_BUILD_TYPE Release)

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++20 -fPIE -Wall -stdlib=libc++ -O3 -flto -fvisibility=hidden -fforce-emit-vtables -fvirtual-function-elimination -fwhole-program-vtables -fintegrated-as")
set(CMAKE_LINK_LIBRARY_FLAG "${CMAKE_LINK_LIBRARY_FLAG} -fuse-ld=lld -static-libstdc++ -Wl,--icf=all,-O3,--lto-O3,--strip-all")
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -fuse-ld=lld -s -Wl,--icf=all,-O3,--lto-O3,--strip-all")

include_directories(
        datasource/
        model/
        utils/
)
file(GLOB_RECURSE SRCS
        "datasource/*.cpp"
        "model/*.cpp"
        "utils/*.cpp"
        "Main.cpp"
        )
add_executable(loadmonitor_gpuv2 ${SRCS})
