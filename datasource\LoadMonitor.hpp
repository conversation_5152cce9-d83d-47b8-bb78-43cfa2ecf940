/*
   Copyright (c) 2022 Ham Jin
   loadmonitor_gpuv2 is licensed under Mulan PSL v2.
   You can use this software according to the terms and conditions of the Mulan PSL v2.
   You may obtain a copy of Mulan PSL v2 at:
               http://license.coscl.org.cn/MulanPSL2
   THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
   See the Mulan PSL v2 for more details.
 */
/*
    Mediatek Mali GPU Load-Based Frequency Adjustment
    File:			LoadMonitor.hpp
    Author: 		HamJin @CoolApk
    Create Date:    2022/03/04
    Last Update: 	2022/04/06
    Feature:        The definition of functions which gets GPU's current load
*/

#pragma once

int DebugUtilization();

void utilization_init();
