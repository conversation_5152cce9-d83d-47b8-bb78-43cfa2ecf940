/*
   Copyright (c) 2022 Ham Jin
   loadmonitor_gpuv2 is licensed under Mulan PSL v2.
   You can use this software according to the terms and conditions of the Mulan PSL v2.
   You may obtain a copy of Mulan PSL v2 at:
               http://license.coscl.org.cn/MulanPSL2
   THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
   See the Mulan PSL v2 for more details.
 */
/*
    Mediatek Mali GPU Load-Based Frequency Adjustment
    File:			GPU.cpp
    Author: 		HamJin @CoolApk
    Create Date: 	2022/08/13
    Last Update: 	2022/12/13
    Feature:		GPU implemention
*/

#include <algorithm>
#include <cstring>

#include "GPU.hpp"
#include "Logger.hpp"
#include "FileOperate.hpp"
#include "FilePath.hpp"

using namespace std;
using namespace logger;

long GPUs::unify_id(long id) {
    if (id < 0)
        return 0;
    if (id >= (long) configList.size())
        return (long) configList.size() - 1;
    return id;
}

long GPUs::readFreqGe(long freq) {
    con_dbg << "readFreqGe=" << freq;
    if (freq <= 0) return *configList.rbegin();
    for (auto &cfreq: configList)
    {
        if (cfreq >= freq) return cfreq;
    }
    return *configList.rbegin();
}

long GPUs::readFreqLe(long freq) {
    con_dbg << "readFreqLe=" << freq;
    if (freq <= 0) return configList.at(0);
    for (auto cfreq = configList.rbegin(); cfreq != configList.rend(); ++cfreq)
        if (*cfreq <= freq) return *cfreq;
    return configList.at(0);
}

long GPUs::readFreqIndex(long freq) {
    if (freq > *configList.rbegin()) return (long) configList.size() - 1;
    if (freq < *configList.begin()) return 0;
    for (int i = 0; i < (int) configList.size(); ++i)
    {
        if (configList[i] == freq) return i;
    }
    return 0;
}

long GPUs::getFreqByIndex(long Idx) {
    return configList.at(unify_id(Idx));
}

long GPUs::getVolt(long freq) {
    return readTab(freqvolt, freq);
}

vector<long> GPUs::getGpufreqList() const {
    return getList(DefFreqList);
}

vector<long> GPUs::getConfigList() const {
    return getList(CfgList);
}

void GPUs::setGpufreqList(vector<long> &gpufreq_List) {
    replaceList(DefFreqList, gpufreq_List);
}

void GPUs::setConfigList(vector<long> &config_List) {
    replaceList(CfgList, config_List);
}

long GPUs::getCurFreq() const {
    return curFreq;
}

void GPUs::setCurFreq(long cur_Freq) {
    GPUs::curFreq = cur_Freq;
}

long GPUs::getCurFreqIdx() const {
    return curFreqIdx;
}

void GPUs::setCurFreqIdx(long cur_FreqIdx) {
    GPUs::curFreqIdx = unify_id(cur_FreqIdx);
}

bool GPUs::isGpuv2() const {
    return gpuv2;
}

void GPUs::setGpuv2(bool gpu_v2) {
    GPUs::gpuv2 = gpu_v2;
}

[[maybe_unused]] bool GPUs::isDcsEnable() const {
    return dcsEnable;
}

void GPUs::setDcsEnable(bool dcs_Enable) {
    GPUs::dcsEnable = dcs_Enable;
}

bool GPUs::isGamingMode() const {
    return gaming_mode;
}

void GPUs::setGamingMode(bool gamingMode) {
    gaming_mode = gamingMode;
}

long GPUs::getCurVolt() const {
    return curVolt;
}

void GPUs::setCurVolt(long cur_Volt) {
    GPUs::curVolt = cur_Volt;
}

long GPUs::genCurVolt() {
    curVolt = getVolt(curFreq);
    return curVolt;
}

long GPUs::conf2tab(long id) const {
    if (id < 0) return -1;
    for (int i = 0; i < (int) gpufreqList.size(); ++i)
    {
        if (configList[id] == gpufreqList[i]) return i;
    }
    return -1;
}


[[maybe_unused]] long GPUs::tab2conf(long id) {
    for (int i = 0; i < (int) configList.size(); ++i)
    {
        if (gpufreqList[id] == configList[i]) return i;
    }
    return -1;
}

long GPUs::genCurFreq(long idx) {
    return configList.at(unify_id(idx));
}

long GPUs::getLoadLow() const {
    return load_low;
}

void GPUs::setLoadLow(long loadLow) {
    if (loadLow > 4000) loadLow = 500;
    load_low = loadLow;
}

bool GPUs::isNeedDcs() const {
    return need_dcs;
}

void GPUs::setNeedDcs(bool needDcs) {
    need_dcs = needDcs;
}

bool GPUs::isIdle() const {
    return is_idle;
}

void GPUs::setIsIdle(bool isIdle) {
    is_idle = isIdle;
}

[[maybe_unused]] bool GPUs::isHaveconfig() const {
    return haveconfig;
}

[[maybe_unused]] void GPUs::setHaveconfig(bool HaveCFG) {
    GPUs::haveconfig = HaveCFG;
}

void GPUs::replaceTab(TabType T, unordered_map<long, long> &Tb) {
    switch (T)
    {
        case freqvolt:
            freqVolt = Tb;
            break;
        case freqdram:
            freqDram = Tb;
            break;
        case defvolt:
            defVolt = Tb;
            break;
        default:
            LogOut_err << " Invalid tab to replace";
    }
}

void GPUs::insertToTab(TabType T, long freq, long data) {
    switch (T)
    {
        case freqvolt:
            if (freqVolt[freq] == 0 || freqVolt[freq] > data) freqVolt[freq] = data;
            break;
        case freqdram:
            if (!(freqDram.find(freq) != freqDram.end() && freqDram.find(freq)->second > data))
                freqDram[freq] = data;
            break;
        case defvolt:
            defVolt[freq] = data;
            break;
        default:
            LogOut_err << " Invalid tab to insert";
    }
}

long GPUs::readTab(TabType T, long freq) {
    switch (T)
    {
        case freqvolt:
            return freqVolt[freq];
        case freqdram:
            return freqDram[freq];
        case defvolt:
            return defVolt[freq];
        default:
            LogOut_err << " Invalid tab to read";

    }
    return 0;
}

[[maybe_unused]] void GPUs::insertToList(ListType T, long freq) {
    switch (T)
    {
        case CfgList:
            configList.push_back(freq);
            std::sort(configList.begin(), configList.end(), less());
            break;
        case DefFreqList:
            gpufreqList.push_back(freq);
            std::sort(gpufreqList.begin(), gpufreqList.end(), less());
            break;
        default:
            LogOut_err << " Invalid list to insert";
    }
}

[[maybe_unused]] void GPUs::replaceList(ListType T, std::vector<long> &list) {
    switch (T)
    {
        case CfgList:
            configList = list;
            break;
        case DefFreqList:
            gpufreqList = list;
            break;
        default:
            LogOut_err << " Invalid list to replace";
    }
}

std::vector<long> GPUs::getList(ListType T) const {
    switch (T)
    {
        case CfgList:
            return configList;
        case DefFreqList:
            return gpufreqList;
        default:
            LogOut_err << " Invalid list to get";
    }
}

constexpr string_view volt_reset = "0 0", opp_reset = "-1", opp_reset_v1 = "0";
enum FreqWriteOpt {
    IDLE,
    NOVOLT,
    DEFAULT
};

static __always_inline void
FreqWrite(const bool &v2, const FreqWriteOpt &opt, const string_view &content) {
    string voltpath, opppath;
    if (v2)
    {
        voltpath = gpufreqv2_volt;
        opppath = gpufreqv2_opp;
    }
    else
    {
        voltpath = gpufreq_volt;
        opppath = gpufreq_opp;
    }
    switch (opt)
    {
        case IDLE:
            con_dbg << "is idle";
            WriteFile(voltpath, volt_reset, volt_reset.length());
            WriteFile(opppath, v2 ? opp_reset : opp_reset_v1,
                      v2 ? opp_reset.length() : opp_reset_v1.length());
            return;
        case NOVOLT:
            con_dbg << "writer has no volt";
            con_dbg << "write " << content << " to opp path";
            WriteFile(voltpath, volt_reset, volt_reset.length());
            WriteFile(opppath, content, content.length());
            return;
        default:
            con_dbg << "write " << content << " to volt path";
            WriteFile(opppath, v2 ? opp_reset : opp_reset_v1,
                      v2 ? opp_reset.length() : opp_reset_v1.length());
            WriteFile(voltpath, content, content.length());
            return;
    }
}

static string last_write;

__always_inline void GPUs::writeFreq() const {
    stringstream sprint;
    if (is_idle)
    {
        FreqWrite(gpuv2, IDLE, sprint.str());
        return;
    }
    if (curVolt == 0)
    {
        if (gpuv2 && need_dcs && curFreqIdx == 0)
        {
            con_dbg << "dcs needed";
            FreqWrite(gpuv2, IDLE, sprint.str());
            return;
        }
        sprint << (gpuv2 ? conf2tab(curFreqIdx) : curFreq);
        if (last_write == sprint.str())
            return;
        FreqWrite(gpuv2, NOVOLT, sprint.str());
        last_write = sprint.str();
        return;
    }
    else
    {
        sprint << curFreq << ' ' << curVolt;
        if (last_write == sprint.str())
            return;
        FreqWrite(gpuv2, DEFAULT, sprint.str());
        last_write = sprint.str();
    }
}

bool GPUs::isPrecise() const {
    return precise;
}

void GPUs::setPrecise(bool Precise) {
    GPUs::precise = Precise;
}
