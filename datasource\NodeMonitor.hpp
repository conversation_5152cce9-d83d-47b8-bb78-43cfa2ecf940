/*
   Copyright (c) 2022 Ham Jin
   loadmonitor_gpuv2 is licensed under Mulan PSL v2.
   You can use this software according to the terms and conditions of the Mulan PSL v2.
   You may obtain a copy of Mulan PSL v2 at:
               http://license.coscl.org.cn/MulanPSL2
   THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
   See the Mulan PSL v2 for more details.
 */
/*
    Mediatek Mali GPU Load-Based Frequency Adjustment
    File:			NodeMonitor.hpp
    Author: 		HamJin @CoolApk
    Create Date:    2022/10/02
    Last Update: 	2022/10/28
    Feature:        Initialize GPU Load Reader and get GPU current load
*/
#pragma once

#include "GPU.hpp"

void monitor_gaming(GPUs &gpu);

void monitor_config(GPUs &gpu);
