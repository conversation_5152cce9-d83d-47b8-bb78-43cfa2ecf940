/*
   Copyright (c) 2022 Ham Jin
   loadmonitor_gpuv2 is licensed under Mulan PSL v2.
   You can use this software according to the terms and conditions of the Mulan PSL v2.
   You may obtain a copy of Mulan PSL v2 at:
               http://license.coscl.org.cn/MulanPSL2
   THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
   See the Mulan PSL v2 for more details.
 */
/*
    Mediatek Mali GPU Load-Based Frequency Adjustment
    File:			FileTester.cpp
    Author: 		HamJin @CoolApk
    Create Date:    2022/03/04
    Last Update: 	2022/08/19
*/


#include "FileOperate.hpp"
#include "FileStatus.hpp"
#include "Logger.hpp"
#include <cstring>
#include <fcntl.h>
#include <map>
#include <string>
#include <sys/stat.h>
#include <unistd.h>

//From AsoulOpt, Thanks nakixii
constexpr int RD_FLAGS = O_RDONLY;
constexpr int WR_FLAGS = O_WRONLY;

constexpr std::string_view DEF_FAIL = "Failed: ";
std::string fail;

std::string_view checkRead(const std::string_view &path, bool &status) {
    if (access(path.data(), R_OK) == 0)
    {
        status = true;
        writeStatus(path, true);
        fail.clear();
        return "OK";
    }
    else
    {
        writeStatus(path, false);
        fail = DEF_FAIL.data();
        fail += strerror(errno);
        return fail.c_str();
    }
}

bool checkRead(const std::string_view &path) {
    if (access(path.data(), R_OK) == 0)
        return true;
    else
        return false;
}

//From AsoulOpt, Thanks nakixii
ssize_t ReadFile(const std::string_view &path, std::string &content, ssize_t maxLen) {
    const int fd = open(path.data(), RD_FLAGS);
    if (fd == -1) [[unlikely]]
        return -1;

    content.resize(maxLen);
    maxLen--;// reserve for EOF

    ssize_t len = 0;
    ssize_t l = 0;
    while (((l = read(fd, content.data() + len, maxLen - len)) > 0) &&
           (len < maxLen))
        len += l;

    close(fd);

    content[len] = '\0';
    content.resize(len);
    return len;
}

ssize_t WriteFile(const std::string_view &path, const std::string_view &content, size_t maxLen) {
    chmod(path.data(), 0644);

    const int fd = open(path.data(), WR_FLAGS);
    if (fd == -1) [[unlikely]]
        return -1;

    ssize_t len = write(fd, content.data(), maxLen);
    close(fd);

    chmod(path.data(), 0444);

    return len;
}