/*
   Copyright (c) 2022 Ham Jin
   loadmonitor_gpuv2 is licensed under Mulan PSL v2.
   You can use this software according to the terms and conditions of the Mulan PSL v2.
   You may obtain a copy of Mulan PSL v2 at:
               http://license.coscl.org.cn/MulanPSL2
   THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
   See the Mulan PSL v2 for more details.
 */
/*
    Mediatek Mali GPU Load-Based Frequency Adjustment
    File:	        inotify.hpp
    Author: 	    yc9559 @Github
    Create Date:    2022/10/10
    Last Update:    2022/12/13
    Feature:        Inotify Watch
    Origin Repo:    yc9559/dfps @Github
*/

#pragma once

#include <fstream>
#include <mutex>
#include <sstream>
#include <string>

struct tm;

namespace logger {
    enum class Level {
        Debug,
        Info,
        Warning,
        Error,
        Fatal
    };

    class FileLogger;

    class ConsoleLogger;

    class BaseLogger;

    class AllLogger;

    class BaseLogger {
        class LogStream;

    public:
        BaseLogger() = default;

        virtual ~BaseLogger() = default;

        virtual LogStream operator()(Level nLevel);

    private:
        const tm *getLocalTime();

        void endline(Level nLevel, const std::string_view &oMessage);

        virtual void output(const tm *p_tm, const std::string_view &str_level,
                            const std::string_view &str_message) = 0;

    private:
        std::mutex _lock;
        tm _localTime;
    };

    class BaseLogger::LogStream : public std::ostringstream {
        BaseLogger &m_oLogger;
        Level m_nLevel;

    public:
        LogStream(BaseLogger &oLogger, Level nLevel)
                : m_oLogger(oLogger), m_nLevel(nLevel) {}

        LogStream(const LogStream &ls)
                : m_oLogger(ls.m_oLogger), m_nLevel(ls.m_nLevel) {}

        ~LogStream() override { m_oLogger.endline(m_nLevel, str()); }
    };

    class ConsoleLogger : public BaseLogger {
        using BaseLogger::BaseLogger;

        virtual void output(const tm *p_tm, const std::string_view &str_level,
                            const std::string_view &str_message) override;
    };

    class FileLogger : public BaseLogger {
    public:
        FileLogger(void) noexcept;

        FileLogger(const FileLogger &) = delete;

        FileLogger(FileLogger &&) = delete;

        virtual ~FileLogger() override;

        void setLogPath(const std::string_view &path) noexcept;

    private:
        virtual void output(const tm *p_tm, const std::string_view &str_level,
                            const std::string_view &str_message) override;

    private:
        std::ofstream _file;
    };

    class AllLogger : public BaseLogger {
    public:
        AllLogger(void) noexcept;

        AllLogger(const AllLogger &) = delete;

        AllLogger(AllLogger &&) = delete;

    private:
        void output(const tm *p_tm, const std::string_view &str_level,
                    const std::string_view &str_message) override;
    };

    extern ConsoleLogger debug;
    extern FileLogger record;
    extern AllLogger LogOut;

}// namespace logger

#define record_info logger::record(logger::Level::Info)
#define record_dbg logger::record(logger::Level::Debug)
#define record_warn logger::record(logger::Level::Warning)
#define record_err logger::record(logger::Level::Error)
#define record_falt logger::record(logger::Level::Fatal)
#define con_info logger::debug(logger::Level::Info)
#define con_dbg logger::debug(logger::Level::Debug)
#define con_warn logger::debug(logger::Level::Warning)
#define con_err logger::debug(logger::Level::Error)
#define con_falt logger::debug(logger::Level::Fatal)
#define LogOut_info logger::LogOut(logger::Level::Info)
#define LogOut_dbg logger::LogOut(logger::Level::Debug)
#define LogOut_warn logger::LogOut(logger::Level::Warning)
#define LogOut_err logger::LogOut(logger::Level::Error)
#define LogOut_falt logger::LogOut(logger::Level::Fatal)
