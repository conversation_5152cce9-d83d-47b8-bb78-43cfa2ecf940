/*
   Copyright (c) 2022 Ham Jin
   loadmonitor_gpuv2 is licensed under Mulan PSL v2.
   You can use this software according to the terms and conditions of the Mulan PSL v2.
   You may obtain a copy of Mulan PSL v2 at:
               http://license.coscl.org.cn/MulanPSL2
   THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
   See the Mulan PSL v2 for more details.
 */
/*
    Mediatek Mali GPU Load-Based Frequency Adjustment
    File:	        inotify.cpp
    Author: 	    yc9559 @Github
    Create Date:    2022/10/10
    Last Update:    2022/12/13
    Feature:        Inotify Watch
    Origin Repo:    yc9559/dfps @Github
*/

#include "Logger.hpp"
#include "FilePath.hpp"

#include <cassert>
#include <cstring>
#include <iomanip>
#include <iostream>
#include <unistd.h>
#include <unordered_map>

using namespace std;
using namespace logger;

ConsoleLogger logger::debug;
FileLogger logger::record;
AllLogger logger::LogOut;

const unordered_map<Level, string_view> LevelStr = {//NOLINT
        {Level::Debug,   "Debug"},
        {Level::Info,    "Info"},
        {Level::Warning, "Warning"},
        {Level::Error,   "Error"},
        {Level::Fatal,   "Fatal"}
};
const unordered_map<string_view, Level> LevelStrR = {//NOLINT
        {"Debug",   Level::Debug},
        {"Info",    Level::Info},
        {"Warning", Level::Warning},
        {"Error",   Level::Error},
        {"Fatal",   Level::Fatal,}
};

ostream &operator<<(ostream &stream, const tm *tm) {
    return stream << 1900 + tm->tm_year << '-' << setfill('0') << setw(2)
                  << tm->tm_mon + 1 << '-' << setfill('0') << setw(2)
                  << tm->tm_mday << ' ' << setfill('0') << setw(2) << tm->tm_hour
                  << ':' << setfill('0') << setw(2) << tm->tm_min << ':'
                  << setfill('0') << setw(2) << tm->tm_sec;
}

BaseLogger::LogStream BaseLogger::operator()(Level nLevel) {
    return LogStream{*this, nLevel};
}

const tm *BaseLogger::getLocalTime() {
    auto now = chrono::system_clock::now();
    auto in_time_t = chrono::system_clock::to_time_t(now);
    localtime_r(&in_time_t, &_localTime);
    return &_localTime;
}

void BaseLogger::endline(Level nLevel, const std::string_view &oMessage) {
#ifndef DEBUG
    if (nLevel == Level::Debug) return;
#endif
    _lock.lock();
    output(getLocalTime(), LevelStr.find(nLevel)->second, oMessage);
    _lock.unlock();
}

//Console logger
void ConsoleLogger::output(const tm *p_tm, const std::string_view &str_level,
                           const std::string_view &str_message) {
    std::ostream::sync_with_stdio(false);
    cout << '[' << p_tm << ']' << '[' << str_level << "]"
         << ": " << str_message << endl;
    cout.flush();
    sync();
}

//File Logger
FileLogger::FileLogger() noexcept: BaseLogger() {
    setLogPath(log_path);
}

FileLogger::~FileLogger() {
    _file.flush();
    _file.close();
}

void FileLogger::setLogPath(const std::string_view &filename) noexcept {
    if (filename.empty())
        return;
    if (_file.is_open())
    {
        _file.flush();
        _file.close();
    }
    _file.open(filename.data(), ios::out | ios::trunc);
    if (!_file.good())
    {
        debug(Level::Fatal) << "Open log file " << filename << " failed!";
        debug(Level::Fatal) << strerror(errno);
        exit(errno);
    }
}

void FileLogger::output(const tm *p_tm, const std::string_view &str_level,
                        const std::string_view &str_message) {
    _file << '[' << p_tm << ']'
          << '[' << str_level << "]"
          << ": " << str_message << endl;
    _file.flush();
    sync();
}

//All Logger
AllLogger::AllLogger() noexcept: BaseLogger() {}

void AllLogger::output(const tm *, const std::string_view &str_level,
                       const std::string_view &str_message) {
    std::ofstream::sync_with_stdio(false);
    debug(LevelStrR.find(str_level.data())->second) << str_message;
    record(LevelStrR.find(str_level.data())->second) << str_message;
}
