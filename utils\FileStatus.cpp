/*
   Copyright (c) 2022 Ham Jin
   loadmonitor_gpuv2 is licensed under Mulan PSL v2.
   You can use this software according to the terms and conditions of the Mulan PSL v2.
   You may obtain a copy of Mulan PSL v2 at:
               http://license.coscl.org.cn/MulanPSL2
   THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
   See the Mulan PSL v2 for more details.
 */
/*
    Mediatek Mali GPU Load-Based Frequency Adjustment
    File:			FileStatus.cpp
    Author: 		HamJin @CoolApk
    Create Date:    2022/09/12
    Last Update: 	2022/09/12
*/
#include "FileStatus.hpp"
#include "GPU.hpp"
#include <unordered_map>

static std::unordered_map<std::string_view, bool> stats;

void writeStatus(const std::string_view &node, bool status) {
    stats[node] = status;
}

bool getStatus(const std::string_view &dir) {
    return stats.at(dir);
}
