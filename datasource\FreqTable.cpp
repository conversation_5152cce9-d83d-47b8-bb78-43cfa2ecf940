/*
   Copyright (c) 2022 Ham Jin
   loadmonitor_gpuv2 is licensed under Mulan PSL v2.
   You can use this software according to the terms and conditions of the Mulan PSL v2.
   You may obtain a copy of Mulan PSL v2 at:
               http://license.coscl.org.cn/MulanPSL2
   THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
   See the Mulan PSL v2 for more details.
 */
/*
	Mediatek Mali GPU Load-Based Frequency Adjustment
	File:			FreqTable.cpp
	Author: 		HamJin @CoolApk
	Create Date: 	2022/03/04
	Last Update: 	2022/12/13
	Feature:		Init GPU frequency table and operate it;
*/

#include "FreqTable.hpp"
#include "FilePath.hpp"
#include "Logger.hpp"
#include <cstring>
#include <fcntl.h>
#include <unistd.h>

using namespace std;
using namespace logger;

static __always_inline void gpuInitv2(GPUs &gpu) {
    ifstream freq_table;
    std::ifstream::sync_with_stdio(false);
    freq_table.open(gpufreqv2_table.data());
    string line;
    vector<long> new_gpufreqList;
    unordered_map<long, long> defVolt;
    while (getline(freq_table, line))
    {
        long freq, volt;
        sscanf(line.c_str(), "[%*d] freq: %lu, volt: %lu", &freq, &volt);//NOLINT
        new_gpufreqList.push_back(freq);
        defVolt[freq] = volt;
    }
    freq_table.close();
    freq_table.clear();
    gpu.setGpufreqList(new_gpufreqList);
    gpu.replaceTab(defvolt, defVolt);
}

static __always_inline void gpuInitv1(GPUs &gpu) {
    ifstream freq_table;
    std::ifstream::sync_with_stdio(false);
    freq_table.open(gpufreq_table.data());
    string line;
    vector<long> new_gpufreqList;
    unordered_map<long, long> defVolt;
    while (getline(freq_table, line))
    {
        long freq, volt;
        sscanf(line.c_str(), "[%*d] freq = %lu, vgpu = %lu", &freq, &volt);//NOLINT
        new_gpufreqList.push_back(freq);
        defVolt[freq] = volt;
    }
    freq_table.close();
    freq_table.clear();
    gpu.setGpufreqList(new_gpufreqList);
    gpu.replaceTab(defvolt, defVolt);
}

void gpufreqTableInit(GPUs &gpu) {
    // Output Table
    for (const auto &freq: gpu.getGpufreqList())
        LogOut_dbg << freq << ":" << gpu.readTab(freqvolt, freq);
    LogOut_info << "gpufreq Max Freq:" << *(gpu.getGpufreqList().cbegin());
    LogOut_info << "gpufreq Min Freq:" << *(gpu.getGpufreqList().crbegin());
    LogOut_info << "gpufreq Total:" << gpu.getGpufreqList().size();
    //gpufreq_v1
    if (access(gpufreq_volt.data(), W_OK) != -1)
    {
        gpu.setGpuv2(false);
        gpu.setDcsEnable(false);
        LogOut_info << "Using gpufreq Driver";
        gpuInitv1(gpu);
        return;
    }
    //gpufreq_v2
    if (access(gpufreqv2_volt.data(), W_OK) != -1)
    {
        gpu.setGpuv2(true);
        gpu.setDcsEnable(true);
        LogOut_info << "Using gpufreqv2 Driver";
        gpuInitv2(gpu);
        return;
    }
    LogOut_falt << "Failed to write GPU freq volt!";
    LogOut_falt << strerror(errno);
    exit(errno);
}
