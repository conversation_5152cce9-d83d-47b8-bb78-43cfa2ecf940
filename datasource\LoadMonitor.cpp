/*
   Copyright (c) 2022 Ham Jin
   loadmonitor_gpuv2 is licensed under Mulan PSL v2.
   You can use this software according to the terms and conditions of the Mulan PSL v2.
   You may obtain a copy of Mulan PSL v2 at:
               http://license.coscl.org.cn/MulanPSL2
   THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
   See the Mulan PSL v2 for more details.
 */
/*
    Mediatek Mali GPU Load-Based Frequency Adjustment
    File:			LoadMonitor.hpp
    Author: 		HamJin @CoolApk
    Create Date:    2022/03/04
    Last Update: 	2022/04/06
    Feature:        Initialize GPU Load Reader and get GPU current load
*/
#include <cstring>
#include <fcntl.h>
#include <thread>
#include "FileStatus.hpp"
#include "FilePath.hpp"
#include "FileOperate.hpp"
#include "Logger.hpp"

using namespace std;
using namespace logger;


static int ModuleGedLoad() {
    if (!getStatus(module_load))
        return -1;
    string buf;
    ReadFile(module_load, buf, 32);
    return stoi(buf);
}

static int ModuleGedIdle() {
    if (!getStatus(module_idle))
        return ModuleGedLoad();
    string buf;
    ReadFile(module_idle, buf, 32);
    con_dbg << "module " << 100 - stoi(buf);
    return 100 - stoi(buf);
}

static int KernelGedLoad() {
    if (!getStatus(kernel_load)) return ModuleGedIdle();
    string buf;
    ReadFile(kernel_load, buf, 32);
    int idle = -1;
    sscanf(buf.data(), "%*d %*d %d", &idle);//NOLINT
    con_dbg << "gedload " << 100 - idle;
    return (100 - idle) == 0 ? ModuleGedLoad() : (100 - idle);
}

static int KernelDebugGedLoad() {
    if (!getStatus(kernel_d_load)) return KernelGedLoad();
    string buf;
    ReadFile(kernel_d_load, buf, 32);
    int idle = -1;
    sscanf(buf.data(), "%*d %*d %d", &idle);//NOLINT
    con_dbg << "dbggedload " << 100 - idle;
    return (100 - idle) == 0 ? KernelGedLoad() : (100 - idle);
}

static int KernelDGedLoad() {
    if (!getStatus(kernel_debug_load))
        return KernelDebugGedLoad();
    string buf;
    ReadFile(kernel_debug_load, buf, 32);
    int idle = -1;
    sscanf(buf.data(), "%*d %*d %d", &idle);//NOLINT
    con_dbg << "dgedload " << 100 - idle;
    return (100 - idle) == 0 ? KernelDebugGedLoad() : (100 - idle);
}

static int MaliLoad() {
    if (!getStatus(proc_mali_load))
        return KernelDGedLoad();
    string buf;
    ReadFile(proc_mali_load, buf, 256);
    int load = 0;
    sscanf(buf.data(), "gpu/cljs0/cljs1=%d", &load);//NOLINT
    con_dbg << "mali " << load;
    return load == 0 ? KernelDGedLoad() : load;
}

static int MtkLoad() {
    if (!getStatus(proc_mtk_load))
        return MaliLoad();
    string buf;
    ReadFile(proc_mtk_load, buf, 256);
    int load = 0;
    sscanf(buf.data(), "ACTIVE=%d", &load);//NOLINT
    con_dbg << "mtk_mali " << load;
    return load == 0 ? MaliLoad() : load;
}

static int GpuFreqLoad() {
    if (!getStatus(gpu_freq_LoadPath)) return MtkLoad();
    FILE *fp = fopen(gpu_freq_LoadPath.data(), "r");
    if (fp == nullptr) [[unlikely]]
    {
        writeStatus(gpu_freq_LoadPath, false);
        return 0;
    }
    char buf[2048];
    size_t buf_sz = sizeof(buf);
    auto buf_ptr = buf;
    int load = -1;
    while (getline(&buf_ptr, &buf_sz, fp) != EOF)
    {
        sscanf(buf, "gpu_loading = %d", &load);//NOLINT
        if (load != -1) break;
    }
    fclose(fp);
    con_dbg << "gpufreq " << load;
    return load == 0 ? MtkLoad() : load;
}

static int DebugUtilizationOLD() {
    if (!getStatus(debug_dvfs_load_old))
        return GpuFreqLoad();
    string buf;
    ReadFile(debug_dvfs_load_old, buf, 256);
    long busy_time = -1, idle_time = 0, protm_time = 0;
    long cur_busy_time = -1, cur_idle_time = 0, cur_protm_time = 0;
    sscanf(buf.data(), "%*s %ld %*s %ld %*s %ld", &busy_time, &idle_time, &protm_time);//NOLINT
    buf.clear();

    this_thread::sleep_for(respTime * 1ms);

    ReadFile(debug_dvfs_load_old, buf, 256);
    sscanf(buf.data(), "%*s %ld %*s %ld %*s %ld", &cur_busy_time, &cur_idle_time,
           &cur_protm_time);//NOLINT

    long diff_busy_time = cur_busy_time - busy_time;
    long diff_idle_time = cur_idle_time - idle_time;
    long diff_protm_time = cur_protm_time - protm_time;

    busy_time = cur_busy_time;
    idle_time = cur_idle_time;
    protm_time = cur_protm_time;

    int load = (int) ((diff_busy_time + diff_protm_time) * 100 /
                      (diff_busy_time + diff_idle_time + diff_protm_time));
    [[unlikely]]if (load < 0) load = 0;
    con_dbg << "debugutil: " << load << ' ' << diff_busy_time << ' ' << diff_idle_time << ' '
            << diff_protm_time;
    return load == 0 ? MtkLoad() : load;
}

int DebugUtilization() {
    if (!getStatus(debug_dvfs_load))
        return DebugUtilizationOLD();
    string buf;
    ReadFile(debug_dvfs_load, buf, 256);
    long busy_time = -1, idle_time = 0, protm_time = 0;
    long cur_busy_time = -1, cur_idle_time = 0, cur_protm_time = 0;
    sscanf(buf.data(), "%*s %ld %*s %ld %*s %ld", &busy_time, &idle_time, &protm_time);//NOLINT
    buf.clear();

    this_thread::sleep_for(respTime * 1ms);

    ReadFile(debug_dvfs_load, buf, 256);
    sscanf(buf.data(), "%*s %ld %*s %ld %*s %ld", &cur_busy_time, &cur_idle_time,
           &cur_protm_time);//NOLINT

    long diff_busy_time = cur_busy_time - busy_time;
    long diff_idle_time = cur_idle_time - idle_time;
    long diff_protm_time = cur_protm_time - protm_time;

    busy_time = cur_busy_time;
    idle_time = cur_idle_time;
    protm_time = cur_protm_time;

    int load = (int) ((diff_busy_time + diff_protm_time) * 100 /
                      (diff_busy_time + diff_idle_time + diff_protm_time));
    [[unlikely]]if (load < 0) load = 0;
    con_dbg << "debugutil: " << load << ' ' << diff_busy_time << ' ' << diff_idle_time << ' '
            << diff_protm_time;
    return load == 0 ? DebugUtilizationOLD() : load;
}

void utilization_init() {
    bool isgood = false;
    LogOut_info << "Init LoadMonitor";
    LogOut_info << "Testing GED...";

    // Method 1: Read From /sys/module/ged
    LogOut_info << module_load << ": " << checkRead(module_load, isgood);
    LogOut_info << module_idle << ": " << checkRead(module_idle, isgood);

    // Method 2: Read From /sys/kernel/ged
    LogOut_info << kernel_load << ": " << checkRead(kernel_load, isgood);

    // Method 3: Read From /sys/kernel/debug/ged
    LogOut_info << kernel_debug_load << ": " << checkRead(kernel_debug_load, isgood);
    LogOut_info << kernel_d_load << ": " << checkRead(kernel_d_load, isgood);

    // Method 4: Read From /proc/gpufreq
    LogOut_info << "Testing gpufreq Driver...";
    LogOut_info << gpu_freq_LoadPath << ": " << checkRead(gpu_freq_LoadPath, isgood);
    // Method 5: Read From Mali Driver
    LogOut_info << "Testing mali driver ...";
    LogOut_info << proc_mtk_load << ": " << checkRead(proc_mtk_load, isgood);
    LogOut_info << proc_mali_load << ": " << checkRead(proc_mali_load, isgood);
    // Method 6: Read precise load from Mali Driver
    LogOut_info << debug_dvfs_load << ": " << checkRead(debug_dvfs_load, isgood);
    LogOut_info << debug_dvfs_load_old << ": " << checkRead(debug_dvfs_load_old, isgood);
    // determine it is OK
    if (!isgood)
    {
        LogOut_falt << "Can't Monitor GPU Loading! ";
        exit(errno);
    }
    LogOut_info << "Test Finished.";
}