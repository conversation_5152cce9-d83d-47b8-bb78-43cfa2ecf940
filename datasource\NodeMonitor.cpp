/*
   Copyright (c) 2022 Ham Jin
   loadmonitor_gpuv2 is licensed under Mulan PSL v2.
   You can use this software according to the terms and conditions of the Mulan PSL v2.
   You may obtain a copy of Mulan PSL v2 at:
               http://license.coscl.org.cn/MulanPSL2
   THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
   See the Mulan PSL v2 for more details.
 */
/*
    Mediatek Mali GPU Load-Based Frequency Adjustment
    File:			NodeMonitor.cpp
    Author: 		HamJin @CoolApk
    Create Date:    2022/10/02
    Last Update: 	2022/12/13
    Feature:        Initialize GPU Load Reader and get GPU current load
*/

#include "ConfigParser.hpp"
#include "FileOperate.hpp"
#include "FilePath.hpp"
#include "inotify.hpp"
#include "Logger.hpp"
#include <cstring>
#include <fcntl.h>
#include <thread>
#include <vector>

using namespace logger;
using namespace std::chrono;

void monitor_gaming(GPUs &gpu) {
    pthread_setname_np(pthread_self(), GameThread.data());
    LogOut_info << GameThread << " Start";
    [[unlikely]] if (!checkRead(GameModePath))
    {
        for (int i = 0; i < 5; ++i)
        {
            std::this_thread::sleep_for(15s);
            if (!checkRead(GameModePath))
                continue;
            goto HAVEASOPT;
        }
        gpu.setGamingMode(true);
        LogOut_warn << "Please enable AsoulOpt for better performance!" << strerror(errno);
        return;
    }
    HAVEASOPT:
    Inotify inotify;
    inotify.Add(GameModePath, Inotify::CLOSE_WRITE | Inotify::MODIFY, nullptr);
    std::string buf;
    while (true)
    {
        inotify.WaitAndHandle();
        ReadFile(GameModePath, buf, 3);
        gpu.setGamingMode(!!stoi(buf));
    }
}

void monitor_config(GPUs &gpu) {
    std::string config_file;
    pthread_setname_np(pthread_self(), ConfThread.data());
    if (checkRead(config_file_tr))
        config_file = config_file_tr;
    else if (checkRead(config_file_def))
        config_file = config_file_def;
    else
    {
        LogOut_err << "CONFIG NOT FOUND:" << strerror(errno);
        LogOut_warn << "Using default freq table";
        GenDefaultFreqTable(gpu);
        return;
    }
    LogOut_info << "Using Config: " << config_file;
    Inotify inotify;
    inotify.Add(config_file, Inotify::CLOSE_WRITE | Inotify::MODIFY, nullptr);
    LogOut_info << ConfThread << " Start";
    config_read(config_file, gpu);
    while (true)
    {
        inotify.WaitAndHandle();
        config_read(config_file, gpu);
    }
}